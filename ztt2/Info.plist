<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>转团团数据文件</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.rainkygong.ztt2.data</string>
			</array>
		</dict>
	</array>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<false/>
	<key>UISupportsDocumentBrowser</key>
	<false/>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh-Hans</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.rainkygong.ztt2</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ztt2</string>
			</array>
		</dict>
	</array>
	<key>LSMinimumSystemVersion</key>
	<string>15.6</string>
	<key>MinimumOSVersion</key>
	<string>15.6</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>api.deepseek.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSUbiquitousContainers</key>
	<dict>
		<key>iCloud.$(CFBundleIdentifier)</key>
		<dict>
			<key>NSUbiquitousContainerIsDocumentScopePublic</key>
			<false/>
			<key>NSUbiquitousContainerName</key>
			<string>转团团数据</string>
			<key>NSUbiquitousContainerSupportedFolderLevels</key>
			<string>Any</string>
		</dict>
	</dict>
	<key>NSUserNotificationsUsageDescription</key>
	<string>转团团需要发送通知来提醒您重要的班级活动和成长记录</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>转团团需要使用麦克风来录制语音并转换为文字，帮助您快速记录成长日记</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>转团团需要使用语音识别功能将您的语音转换为文字，让记录更加便捷</string>

	<!-- 后台模式配置 -->
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>

	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string></string>
		<key>UIImageName</key>
		<string></string>
		<key>UILaunchScreenBackgroundColor</key>
		<string>#fcfff4</string>
	</dict>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>

	<!-- 支持的界面方向 -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>

	<!-- iPad支持的界面方向 -->
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
</dict>
</plist>
