//
//  TrialManagerTests.swift
//  ztt2Tests
//
//  Created by AI Assistant on 2025/8/5.
//

import XCTest
@testable import ztt2

/**
 * 试用管理器测试
 * 验证试用功能的核心逻辑
 */
@MainActor
final class TrialManagerTests: XCTestCase {
    
    var trialManager: TrialManager!
    
    override func setUp() {
        super.setUp()
        // 注意：由于TrialManager是单例，我们需要重置其状态
        trialManager = TrialManager.shared
        
        // 清理测试环境
        clearTrialData()
    }
    
    override func tearDown() {
        clearTrialData()
        super.tearDown()
    }
    
    // MARK: - Helper Methods
    
    private func clearTrialData() {
        // 清理NSUbiquitousKeyValueStore
        let ubiquitousStore = NSUbiquitousKeyValueStore.default
        ubiquitousStore.removeObject(forKey: "hasReceivedTrial")
        ubiquitousStore.removeObject(forKey: "trialExpirationDate")
        ubiquitousStore.synchronize()
        
        // 清理UserDefaults
        UserDefaults.standard.removeObject(forKey: "local_hasReceivedTrial")
        UserDefaults.standard.removeObject(forKey: "local_trialExpirationDate")
        UserDefaults.standard.synchronize()
    }
    
    // MARK: - Test Cases
    
    /**
     * 测试初始状态
     */
    func testInitialState() {
        XCTAssertFalse(trialManager.hasReceivedTrial, "初始状态应该未领取试用")
        XCTAssertFalse(trialManager.isTrialActive, "初始状态试用应该未激活")
        XCTAssertNil(trialManager.trialExpirationDate, "初始状态到期时间应该为空")
        XCTAssertTrue(trialManager.canClaimTrial(), "初始状态应该可以领取试用")
    }
    
    /**
     * 测试试用领取
     */
    func testClaimTrial() async {
        // 验证可以领取试用
        XCTAssertTrue(trialManager.canClaimTrial(), "应该可以领取试用")
        
        // 领取试用
        let success = await trialManager.claimTrial()
        
        // 验证领取成功
        XCTAssertTrue(success, "试用领取应该成功")
        XCTAssertTrue(trialManager.hasReceivedTrial, "应该标记为已领取试用")
        XCTAssertTrue(trialManager.isTrialActive, "试用应该激活")
        XCTAssertNotNil(trialManager.trialExpirationDate, "应该设置到期时间")
        
        // 验证到期时间正确（30天后）
        if let expirationDate = trialManager.trialExpirationDate {
            let expectedDate = Calendar.current.date(byAdding: .day, value: 30, to: Date())!
            let timeDifference = abs(expirationDate.timeIntervalSince(expectedDate))
            XCTAssertLessThan(timeDifference, 60, "到期时间应该是30天后（误差小于1分钟）")
        }
    }
    
    /**
     * 测试重复领取试用
     */
    func testCannotClaimTrialTwice() async {
        // 第一次领取
        let firstClaim = await trialManager.claimTrial()
        XCTAssertTrue(firstClaim, "第一次领取应该成功")
        
        // 第二次领取应该失败
        let secondClaim = await trialManager.claimTrial()
        XCTAssertFalse(secondClaim, "第二次领取应该失败")
        XCTAssertFalse(trialManager.canClaimTrial(), "已领取后不应该可以再次领取")
    }
    
    /**
     * 测试试用剩余天数计算
     */
    func testRemainingTrialDays() async {
        // 领取试用
        await trialManager.claimTrial()
        
        // 验证剩余天数
        let remainingDays = trialManager.getRemainingTrialDays()
        XCTAssertGreaterThanOrEqual(remainingDays, 29, "剩余天数应该接近30天")
        XCTAssertLessThanOrEqual(remainingDays, 30, "剩余天数不应该超过30天")
    }
    
    /**
     * 测试试用状态显示信息
     */
    func testTrialDisplayInfo() async {
        // 未领取状态
        var displayInfo = trialManager.getTrialDisplayInfo()
        XCTAssertEqual(displayInfo.status, .available, "未领取时状态应该是available")
        XCTAssertEqual(displayInfo.buttonText, "trial.button.claim".localized, "按钮文案应该是立即领取")
        
        // 领取试用
        await trialManager.claimTrial()
        
        // 激活状态
        displayInfo = trialManager.getTrialDisplayInfo()
        XCTAssertEqual(displayInfo.status, .active, "领取后状态应该是active")
        XCTAssertEqual(displayInfo.buttonText, "profile.subscription.view_plans_button".localized, "按钮文案应该是查看方案")
    }
    
    /**
     * 测试试用有效性检查
     */
    func testTrialValidityCheck() async {
        // 领取试用
        await trialManager.claimTrial()
        
        // 验证试用有效
        trialManager.checkTrialValidity()
        XCTAssertTrue(trialManager.isTrialActive, "试用应该有效")
        
        // 模拟过期（设置过去的时间）
        let pastDate = Calendar.current.date(byAdding: .day, value: -1, to: Date())!
        trialManager.trialExpirationDate = pastDate
        
        // 检查有效性
        trialManager.checkTrialValidity()
        XCTAssertFalse(trialManager.isTrialActive, "过期的试用应该无效")
    }
}
