# 试用状态持久化问题修复说明

## 问题描述
在个人中心页面领取试用后，会变成高级会员，但重启应用后，会变回免费用户。

## 问题原因分析

### 1. 数据加载时序问题
- TrialManager在初始化时立即尝试加载试用状态
- 但DataManager的currentUser可能还没有完全初始化
- 导致从CoreData加载试用状态失败

### 2. 数据同步机制不完善
- 缺少对用户状态变化的监听
- 应用启动时没有正确的数据恢复机制

## 修复方案

### 1. 优化初始化时序
```swift
// 修改前：立即加载
private init() {
    setupObservers()
    forceCloudSync()
    loadTrialStatus()  // 可能失败，因为用户还没加载
    checkTrialValidity()
    setupValidityCheckTimer()
}

// 修改后：延迟加载
private init() {
    setupObservers()
    forceCloudSync()
    
    // 延迟加载试用状态，等待DataManager初始化完成
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
        self.loadTrialStatus()
        self.checkTrialValidity()
    }
    
    setupValidityCheckTimer()
}
```

### 2. 添加用户状态监听
```swift
// 监听DataManager的currentUser变化
dataManager.$currentUser
    .sink { [weak self] user in
        if user != nil {
            print("📱 检测到用户状态变化，重新初始化试用状态")
            self?.initializeTrialStatus()
        }
    }
    .store(in: &cancellables)
```

### 3. 增强数据保存验证
```swift
// 保存后验证数据是否正确写入
if let savedSubscription = user.subscription {
    print("✅ 保存验证 - 试用状态: \(savedSubscription.hasReceivedTrial), 到期时间: \(savedSubscription.expirationDate?.description ?? "无")")
}
```

### 4. 添加应用生命周期监听
```swift
.onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
    // 应用从后台回到前台时重新检查试用状态
    print("📱 应用回到前台，重新检查试用状态")
    trialManager.forceReloadTrialStatus()
}
```

## 测试步骤

### 1. 基本功能测试
1. 启动应用，进入个人中心
2. 确认显示"你有一个限时福利可领取"
3. 点击"立即领取"，确认弹出试用弹窗
4. 点击"立即领取"确认领取
5. 确认显示"高级会员（试用中）"和到期时间

### 2. 重启持久化测试
1. 完成基本功能测试后，完全关闭应用
2. 重新启动应用
3. 进入个人中心
4. **关键验证点**：确认仍显示"高级会员（试用中）"
5. 确认到期时间正确显示

### 3. 多设备同步测试（如果有多台设备）
1. 在设备A领取试用
2. 在设备B打开应用
3. 确认设备B也显示已领取状态

### 4. 调试信息查看
1. 在个人中心页面长按3秒（任意位置）
2. 会弹出调试信息弹窗
3. 查看各种存储状态是否一致：
   - 当前状态：已领取试用 = true，试用激活 = true
   - 存储状态：云端、本地、CoreData都应该有正确数据
   - CoreData状态：订阅-已领取试用 = true，订阅-到期时间有值

### 5. 故障恢复测试
如果发现状态不一致，可以在调试弹窗中：
1. 点击"强制重新加载状态"
2. 点击"刷新试用状态"
3. 点击"初始化试用状态"

## 关键修复点

### 1. 数据模型更新
- 为Subscription实体添加了`hasReceivedTrial`和`expirationDate`字段
- 确保试用状态正确存储在CoreData中

### 2. 三重存储机制
- **NSUbiquitousKeyValueStore**：多设备同步
- **UserDefaults**：本地备份存储
- **CoreData**：主要数据存储

### 3. 数据优先级
- CoreData > 云端 > 本地
- 确保使用最可靠的数据源

### 4. 详细日志
- 添加了详细的调试日志
- 便于排查问题和验证修复效果

## 预期结果

修复后应该实现：
1. ✅ 试用领取后立即生效
2. ✅ 重启应用后状态保持
3. ✅ 多设备状态同步
4. ✅ 数据一致性保证
5. ✅ 故障自动恢复

## 如果问题仍然存在

请查看控制台日志，关注以下信息：
- "📱 开始加载试用状态..."
- "📱 CoreData数据 - 用户: xxx, 已领取: xxx, 到期时间: xxx"
- "✅ 保存验证 - 试用状态: xxx, 到期时间: xxx"

如果看到用户为"无"或数据不一致，说明DataManager初始化有问题，需要进一步调试。
