//
//  TrialDebugView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import SwiftUI

/**
 * 试用状态调试视图
 * 用于调试和查看试用功能的详细状态信息
 */
struct TrialDebugView: View {
    
    // MARK: - Properties
    @ObservedObject var trialManager: TrialManager
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var dataManager = DataManager.shared
    
    // MARK: - State
    @State private var refreshCount = 0
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    
                    // 标题
                    Text("试用状态调试信息")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.bottom, 10)
                    
                    // 当前状态
                    debugSection(title: "当前状态") {
                        debugRow("已领取试用", value: "\(trialManager.hasReceivedTrial)")
                        debugRow("试用激活", value: "\(trialManager.isTrialActive)")
                        debugRow("到期时间", value: trialManager.trialExpirationDate?.description ?? "无")
                        debugRow("剩余天数", value: "\(trialManager.getRemainingTrialDays())")
                        debugRow("加载状态", value: "\(trialManager.isLoading)")
                        debugRow("错误信息", value: trialManager.errorMessage ?? "无")
                    }
                    
                    // 存储状态
                    debugSection(title: "存储状态") {
                        let ubiquitousStore = NSUbiquitousKeyValueStore.default
                        let cloudHasReceived = ubiquitousStore.bool(forKey: "hasReceivedTrial")
                        let cloudExpirationDate = ubiquitousStore.object(forKey: "trialExpirationDate") as? Date
                        
                        let localHasReceived = UserDefaults.standard.bool(forKey: "local_hasReceivedTrial")
                        let localExpirationDate = UserDefaults.standard.object(forKey: "local_trialExpirationDate") as? Date
                        
                        debugRow("云端-已领取", value: "\(cloudHasReceived)")
                        debugRow("云端-到期时间", value: cloudExpirationDate?.description ?? "无")
                        debugRow("本地-已领取", value: "\(localHasReceived)")
                        debugRow("本地-到期时间", value: localExpirationDate?.description ?? "无")
                    }
                    
                    // CoreData状态
                    debugSection(title: "CoreData状态") {
                        let user = dataManager.currentUser
                        let subscription = user?.subscription
                        
                        debugRow("用户ID", value: user?.id?.uuidString ?? "无")
                        debugRow("用户昵称", value: user?.nickname ?? "无")
                        debugRow("订阅类型", value: user?.subscriptionType ?? "无")
                        debugRow("订阅-已领取试用", value: "\(subscription?.hasReceivedTrial ?? false)")
                        debugRow("订阅-到期时间", value: subscription?.expirationDate?.description ?? "无")
                        debugRow("订阅-激活状态", value: "\(subscription?.isActive ?? false)")
                        debugRow("订阅-级别", value: subscription?.level ?? "无")
                    }
                    
                    // 显示信息
                    debugSection(title: "显示信息") {
                        let displayInfo = trialManager.getTrialDisplayInfo()
                        debugRow("状态", value: "\(displayInfo.status)")
                        debugRow("消息", value: displayInfo.message)
                        debugRow("按钮文案", value: displayInfo.buttonText)
                        debugRow("可领取", value: "\(trialManager.canClaimTrial())")
                    }
                    
                    // 操作按钮
                    VStack(spacing: 12) {
                        Button("强制重新加载状态") {
                            trialManager.forceReloadTrialStatus()
                            refreshCount += 1
                        }
                        .buttonStyle(.borderedProminent)
                        
                        Button("刷新试用状态") {
                            trialManager.refreshTrialStatus()
                            refreshCount += 1
                        }
                        .buttonStyle(.bordered)
                        
                        Button("初始化试用状态") {
                            trialManager.initializeTrialStatus()
                            refreshCount += 1
                        }
                        .buttonStyle(.bordered)
                        
                        if trialManager.canClaimTrial() {
                            Button("测试领取试用") {
                                Task {
                                    let success = await trialManager.claimTrial()
                                    print("测试领取结果: \(success)")
                                    refreshCount += 1
                                }
                            }
                            .buttonStyle(.borderedProminent)
                            .foregroundColor(.white)
                        }
                    }
                    .padding(.top, 20)
                    
                    // 刷新计数
                    Text("刷新次数: \(refreshCount)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.top, 10)
                }
                .padding()
            }
            .navigationTitle("试用调试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Views
    
    @ViewBuilder
    private func debugSection<Content: View>(title: String, @ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(alignment: .leading, spacing: 4) {
                content()
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    private func debugRow(_ label: String, value: String) -> some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 100, alignment: .leading)
            
            Text(value)
                .font(.caption)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
    }
}

// MARK: - Preview
#Preview {
    TrialDebugView(trialManager: TrialManager.shared)
}
