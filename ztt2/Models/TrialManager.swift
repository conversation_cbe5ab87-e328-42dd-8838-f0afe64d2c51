//
//  TrialManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/5.
//

import Foundation
import Combine
import CloudKit

/**
 * 试用管理器
 * 负责管理30天高级会员试用功能
 * 使用NSUbiquitousKeyValueStore进行多设备同步
 * 与CoreData结合存储试用状态和到期时间
 */
@MainActor
class TrialManager: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = TrialManager()
    
    // MARK: - Published Properties
    @Published var hasReceivedTrial: Bool = false
    @Published var isTrialActive: Bool = false
    @Published var trialExpirationDate: Date?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Constants
    private let trialDurationDays = 30
    private let hasReceivedTrialKey = "hasReceivedTrial"
    private let trialExpirationKey = "trialExpirationDate"

    // 本地备份存储键（用于NSUbiquitousKeyValueStore同步延迟的情况）
    private let localHasReceivedTrialKey = "local_hasReceivedTrial"
    private let localTrialExpirationKey = "local_trialExpirationDate"
    
    // MARK: - Private Properties
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    private var validityCheckTimer: Timer?

    // MARK: - Initialization
    private init() {
        setupObservers()

        // 强制同步NSUbiquitousKeyValueStore
        forceCloudSync()

        // 延迟加载试用状态，等待DataManager初始化完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.loadTrialStatus()

            // 启动时检查试用有效性
            self.checkTrialValidity()
        }

        // 设置定期检查定时器（每小时检查一次）
        setupValidityCheckTimer()
    }

    deinit {
        validityCheckTimer?.invalidate()
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查用户是否可以领取试用
     */
    func canClaimTrial() -> Bool {
        return !hasReceivedTrial
    }
    
    /**
     * 领取试用
     */
    func claimTrial() async -> Bool {
        guard canClaimTrial() else {
            errorMessage = "trial.error.already_claimed".localized
            return false
        }
        
        isLoading = true
        
        // 计算试用到期时间
        let expirationDate = Calendar.current.date(byAdding: .day, value: trialDurationDays, to: Date())!

        // 更新本地状态
        hasReceivedTrial = true
        isTrialActive = true
        trialExpirationDate = expirationDate

        // 保存到NSUbiquitousKeyValueStore（多设备同步）
        ubiquitousStore.set(true, forKey: hasReceivedTrialKey)
        ubiquitousStore.set(expirationDate, forKey: trialExpirationKey)
        ubiquitousStore.synchronize()

        // 同时保存到本地UserDefaults（备份存储）
        UserDefaults.standard.set(true, forKey: localHasReceivedTrialKey)
        UserDefaults.standard.set(expirationDate, forKey: localTrialExpirationKey)
        UserDefaults.standard.synchronize()

        // 更新CoreData中的订阅信息
        await updateSubscriptionInCoreData(expirationDate: expirationDate)

        isLoading = false

        print("✅ 试用领取成功，到期时间: \(expirationDate)")
        return true
    }
    
    /**
     * 检查试用是否有效
     */
    func checkTrialValidity() {
        guard hasReceivedTrial, let expirationDate = trialExpirationDate else {
            isTrialActive = false
            return
        }
        
        let now = Date()
        let wasActive = isTrialActive
        isTrialActive = now < expirationDate
        
        // 如果试用刚过期，更新CoreData状态
        if wasActive && !isTrialActive {
            Task {
                await handleTrialExpiration()
            }
        }
    }
    
    /**
     * 获取试用剩余天数
     */
    func getRemainingTrialDays() -> Int {
        guard isTrialActive, let expirationDate = trialExpirationDate else {
            return 0
        }
        
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: now, to: expirationDate)
        return max(0, components.day ?? 0)
    }
    
    /**
     * 强制刷新试用状态（用于应用启动时确保状态正确）
     */
    func refreshTrialStatus() {
        print("🔄 强制刷新试用状态...")
        forceCloudSync()

        // 延迟一点时间让同步完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.loadTrialStatus()
        }
    }

    /**
     * 初始化试用状态（在DataManager准备好后调用）
     */
    func initializeTrialStatus() {
        print("🔄 初始化试用状态...")
        loadTrialStatus()
        checkTrialValidity()
    }

    /**
     * 强制重新加载试用状态（用于调试和故障恢复）
     */
    func forceReloadTrialStatus() {
        print("🔄 强制重新加载试用状态...")

        // 清空当前状态
        hasReceivedTrial = false
        isTrialActive = false
        trialExpirationDate = nil

        // 重新加载
        loadTrialStatus()
        checkTrialValidity()

        print("✅ 试用状态强制重新加载完成")
    }

    /**
     * 获取试用状态显示信息
     */
    func getTrialDisplayInfo() -> TrialDisplayInfo {
        if !hasReceivedTrial {
            return TrialDisplayInfo(
                status: .available,
                message: "trial.status.available".localized,
                buttonText: "trial.button.claim".localized,
                expirationDate: nil
            )
        } else if isTrialActive {
            let remainingDays = getRemainingTrialDays()
            return TrialDisplayInfo(
                status: .active,
                message: "trial.status.active".localized(with: remainingDays),
                buttonText: "profile.subscription.view_plans_button".localized,
                expirationDate: trialExpirationDate
            )
        } else {
            return TrialDisplayInfo(
                status: .expired,
                message: "trial.status.expired".localized,
                buttonText: "profile.subscription.view_plans_button".localized,
                expirationDate: trialExpirationDate
            )
        }
    }

    // MARK: - Private Methods

    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听NSUbiquitousKeyValueStore变化
        NotificationCenter.default.publisher(for: NSUbiquitousKeyValueStore.didChangeExternallyNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleExternalStoreChange()
                }
            }
            .store(in: &cancellables)

        // 监听DataManager的currentUser变化
        dataManager.$currentUser
            .sink { [weak self] user in
                if user != nil {
                    print("📱 检测到用户状态变化，重新初始化试用状态")
                    self?.initializeTrialStatus()
                }
            }
            .store(in: &cancellables)
    }

    /**
     * 加载试用状态
     */
    private func loadTrialStatus() {
        print("📱 开始加载试用状态...")

        // 首先尝试从NSUbiquitousKeyValueStore加载
        let cloudHasReceived = ubiquitousStore.bool(forKey: hasReceivedTrialKey)
        let cloudExpirationDate = ubiquitousStore.object(forKey: trialExpirationKey) as? Date
        print("📱 云端数据 - 已领取: \(cloudHasReceived), 到期时间: \(cloudExpirationDate?.description ?? "无")")

        // 从本地UserDefaults加载备份数据
        let localHasReceived = UserDefaults.standard.bool(forKey: localHasReceivedTrialKey)
        let localExpirationDate = UserDefaults.standard.object(forKey: localTrialExpirationKey) as? Date
        print("📱 本地数据 - 已领取: \(localHasReceived), 到期时间: \(localExpirationDate?.description ?? "无")")

        // 从CoreData加载最终备份数据
        let currentUser = dataManager.currentUser
        let coreDataHasReceived = currentUser?.subscription?.hasReceivedTrial ?? false
        let coreDataExpirationDate = currentUser?.subscription?.expirationDate
        print("📱 CoreData数据 - 用户: \(currentUser?.id?.uuidString ?? "无"), 已领取: \(coreDataHasReceived), 到期时间: \(coreDataExpirationDate?.description ?? "无")")

        // 使用最可靠的数据源（优先级：CoreData > 云端 > 本地）
        if coreDataHasReceived || coreDataExpirationDate != nil {
            // CoreData有数据，使用CoreData数据（最可靠）
            hasReceivedTrial = coreDataHasReceived
            trialExpirationDate = coreDataExpirationDate

            // 同步到其他存储
            ubiquitousStore.set(coreDataHasReceived, forKey: hasReceivedTrialKey)
            UserDefaults.standard.set(coreDataHasReceived, forKey: localHasReceivedTrialKey)
            if let coreDataDate = coreDataExpirationDate {
                ubiquitousStore.set(coreDataDate, forKey: trialExpirationKey)
                UserDefaults.standard.set(coreDataDate, forKey: localTrialExpirationKey)
            }
            ubiquitousStore.synchronize()
            UserDefaults.standard.synchronize()

            print("📱 从CoreData加载试用状态（最可靠）")
        } else if cloudHasReceived || cloudExpirationDate != nil {
            // 云端有数据，使用云端数据
            hasReceivedTrial = cloudHasReceived
            trialExpirationDate = cloudExpirationDate

            // 同步到本地备份和CoreData
            UserDefaults.standard.set(cloudHasReceived, forKey: localHasReceivedTrialKey)
            if let cloudDate = cloudExpirationDate {
                UserDefaults.standard.set(cloudDate, forKey: localTrialExpirationKey)
            }
            UserDefaults.standard.synchronize()

            // 同步到CoreData
            if let subscription = dataManager.currentUser?.subscription {
                subscription.hasReceivedTrial = cloudHasReceived
                subscription.expirationDate = cloudExpirationDate
                dataManager.saveContext()
            }

            print("📱 从云端加载试用状态")
        } else if localHasReceived || localExpirationDate != nil {
            // 本地有数据，使用本地备份数据
            hasReceivedTrial = localHasReceived
            trialExpirationDate = localExpirationDate

            // 同步到云端和CoreData
            ubiquitousStore.set(localHasReceived, forKey: hasReceivedTrialKey)
            if let localDate = localExpirationDate {
                ubiquitousStore.set(localDate, forKey: trialExpirationKey)
            }
            ubiquitousStore.synchronize()

            // 同步到CoreData
            if let subscription = dataManager.currentUser?.subscription {
                subscription.hasReceivedTrial = localHasReceived
                subscription.expirationDate = localExpirationDate
                dataManager.saveContext()
            }

            print("📱 从本地备份加载试用状态")
        } else {
            // 都没有数据，初始状态
            hasReceivedTrial = false
            trialExpirationDate = nil
            print("📱 初始化试用状态")
        }

        // 检查试用有效性
        checkTrialValidity()

        print("📱 试用状态加载完成 - 已领取: \(hasReceivedTrial), 激活中: \(isTrialActive)")
    }

    /**
     * 处理外部存储变化（多设备同步）
     */
    private func handleExternalStoreChange() {
        print("🔄 检测到试用状态外部变化，重新加载...")

        // 获取云端最新数据
        let cloudHasReceived = ubiquitousStore.bool(forKey: hasReceivedTrialKey)
        let cloudExpirationDate = ubiquitousStore.object(forKey: trialExpirationKey) as? Date

        // 更新本地状态
        hasReceivedTrial = cloudHasReceived
        trialExpirationDate = cloudExpirationDate

        // 同步到本地备份
        UserDefaults.standard.set(cloudHasReceived, forKey: localHasReceivedTrialKey)
        if let cloudDate = cloudExpirationDate {
            UserDefaults.standard.set(cloudDate, forKey: localTrialExpirationKey)
        } else {
            UserDefaults.standard.removeObject(forKey: localTrialExpirationKey)
        }
        UserDefaults.standard.synchronize()

        // 检查试用有效性
        checkTrialValidity()

        print("📱 外部变化处理完成 - 已领取: \(hasReceivedTrial), 激活中: \(isTrialActive)")
    }

    /**
     * 更新CoreData中的订阅信息
     */
    private func updateSubscriptionInCoreData(expirationDate: Date) async {
        guard let user = dataManager.currentUser else {
            print("❌ 无法获取当前用户")
            return
        }

        // 记录旧的订阅级别
        let oldLevel = user.subscription?.subscriptionType ?? "free"

        // 确保用户有订阅记录
        if user.subscription == nil {
            // 创建新的订阅记录
            dataManager.updateSubscription(
                type: "premium",
                isActive: true,
                startDate: Date(),
                endDate: expirationDate,
                productIdentifier: "trial_premium"
            )
        }

        // 确保订阅记录存在后再设置试用字段
        if let subscription = user.subscription {
            subscription.subscriptionType = "premium"
            subscription.level = "premium"
            subscription.isActive = true
            subscription.endDate = expirationDate
            subscription.hasReceivedTrial = true
            subscription.expirationDate = expirationDate
            subscription.updatedAt = Date()

            print("✅ 订阅记录已更新 - 类型: \(subscription.subscriptionType ?? "未知"), 试用: \(subscription.hasReceivedTrial), 到期: \(subscription.expirationDate?.description ?? "无")")
        } else {
            print("❌ 订阅记录创建失败")
            return
        }

        // 更新用户的订阅类型
        user.subscriptionType = "premium"
        user.updatedAt = Date()

        // 保存到CoreData
        dataManager.saveContext()

        // 验证保存结果
        if let savedSubscription = user.subscription {
            print("✅ 保存验证 - 试用状态: \(savedSubscription.hasReceivedTrial), 到期时间: \(savedSubscription.expirationDate?.description ?? "无")")
        }

        print("✅ CoreData订阅信息已更新为试用高级会员")

        // 触发订阅升级通知
        await MainActor.run {
            NotificationCenter.default.post(
                name: NSNotification.Name("SubscriptionUpgraded"),
                object: nil,
                userInfo: [
                    "userId": user.id?.uuidString ?? "",
                    "oldLevel": oldLevel,
                    "newLevel": "premium"
                ]
            )
        }
    }

    /**
     * 处理试用过期
     */
    private func handleTrialExpiration() async {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription else {
            return
        }

        // 记录旧的订阅级别
        let oldLevel = subscription.subscriptionType ?? "premium"

        // 将订阅降级为免费版
        subscription.subscriptionType = "free"
        subscription.level = "free"
        subscription.isActive = false
        subscription.endDate = nil

        // 更新用户的订阅类型
        user.subscriptionType = "free"

        // 保存到CoreData
        dataManager.saveContext()

        print("⏰ 试用已过期，订阅已降级为免费版")

        // 触发订阅降级通知
        await MainActor.run {
            NotificationCenter.default.post(
                name: NSNotification.Name("SubscriptionDowngraded"),
                object: nil,
                userInfo: [
                    "userId": user.id?.uuidString ?? "",
                    "oldLevel": oldLevel,
                    "newLevel": "free"
                ]
            )
        }
    }

    /**
     * 设置试用有效性检查定时器
     */
    private func setupValidityCheckTimer() {
        validityCheckTimer = Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.checkTrialValidity()
            }
        }
    }

    /**
     * 强制同步NSUbiquitousKeyValueStore
     */
    private func forceCloudSync() {
        // 强制同步云端存储
        ubiquitousStore.synchronize()

        // 等待一小段时间让同步完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 再次尝试同步
            self.ubiquitousStore.synchronize()
        }

        print("🔄 强制同步NSUbiquitousKeyValueStore")
    }
}

// MARK: - Trial Display Info

struct TrialDisplayInfo {
    let status: TrialStatus
    let message: String
    let buttonText: String
    let expirationDate: Date?
}

enum TrialStatus {
    case available  // 可领取
    case active     // 试用中
    case expired    // 已过期
}
